/**
 * 基础API和设备管理功能测试
 */

import { buildApplyPrivateKeyRequest } from '../app/api/custom-api/base-api';
import {
  getDeviceNo,
  regenerateDeviceNo,
  clearDeviceNo,
  hasDeviceNo,
  getDevicePlatform,
  validateDeviceNo
} from '../app/utils/device';

describe('API Request Building Tests', () => {
  beforeEach(() => {
    // 每个测试前清除设备号，确保测试独立性
    clearDeviceNo();
  });

  test('should generate correct request parameters with provided deviceNo', () => {
    const deviceNo = 'TEST_DEVICE_001';

    const request = buildApplyPrivateKeyRequest(deviceNo);

    // 验证返回的对象结构
    expect(request).toHaveProperty('deviceNo');
    expect(request).toHaveProperty('masterKey');
    expect(request).toHaveProperty('sign');

    // 验证deviceNo正确传递
    expect(request.deviceNo).toBe(deviceNo);

    // 验证masterKey以04开头（根据加密步骤4的要求）
    expect(request.masterKey).toMatch(/^04/);
    expect(request.masterKey.length).toBeGreaterThan(10);

    // 验证sign不为空且为字符串
    expect(request.sign).toBeTruthy();
    expect(typeof request.sign).toBe('string');
    expect(request.sign.length).toBeGreaterThan(0);
  });

  test('buildApplyPrivateKeyRequest should auto-generate deviceNo when not provided', () => {
    const request = buildApplyPrivateKeyRequest();

    // 验证返回的对象结构
    expect(request).toHaveProperty('deviceNo');
    expect(request).toHaveProperty('masterKey');
    expect(request).toHaveProperty('sign');

    // 验证deviceNo被自动生成
    expect(request.deviceNo).toBeTruthy();
    expect(typeof request.deviceNo).toBe('string');

    // 验证设备号格式（应该是android_或ios_开头）
    expect(request.deviceNo).toMatch(/^(android_|ios_)/);

    console.log('Auto-generated device number:', request.deviceNo);
  });

  test('buildApplyPrivateKeyRequest should generate different signs for different deviceNo', () => {
    const deviceNo1 = 'DEVICE_001';
    const deviceNo2 = 'DEVICE_002';

    const request1 = buildApplyPrivateKeyRequest(deviceNo1);
    const request2 = buildApplyPrivateKeyRequest(deviceNo2);

    // 不同的deviceNo应该生成不同的sign
    expect(request1.sign).not.toBe(request2.sign);

    // masterKey应该相同（因为在同一次运行中使用相同的随机生成的MASTER_KEY）
    expect(request1.masterKey).toBe(request2.masterKey);
  });

  test('buildApplyPrivateKeyRequest should be consistent for same deviceNo', () => {
    const deviceNo = 'CONSISTENT_DEVICE';

    const request1 = buildApplyPrivateKeyRequest(deviceNo);
    const request2 = buildApplyPrivateKeyRequest(deviceNo);

    // 相同的deviceNo应该生成相同的结果
    expect(request1.deviceNo).toBe(request2.deviceNo);
    expect(request1.masterKey).toBe(request2.masterKey);
    expect(request1.sign).toBe(request2.sign);
  });

  test('buildApplyPrivateKeyRequest should use same auto-generated deviceNo across calls', () => {
    const request1 = buildApplyPrivateKeyRequest();
    const request2 = buildApplyPrivateKeyRequest();

    // 自动生成的设备号应该在多次调用中保持一致
    expect(request1.deviceNo).toBe(request2.deviceNo);
    expect(request1.sign).toBe(request2.sign);
  });
});

describe('Device Management Tests', () => {
  beforeEach(() => {
    clearDeviceNo();
  });

  test('getDeviceNo should generate and persist device number', () => {
    const deviceNo1 = getDeviceNo();
    const deviceNo2 = getDeviceNo();

    // 应该生成有效的设备号
    expect(deviceNo1).toBeTruthy();
    expect(typeof deviceNo1).toBe('string');

    // 多次调用应该返回相同的设备号
    expect(deviceNo1).toBe(deviceNo2);

    // 验证设备号格式
    expect(deviceNo1).toMatch(/^(android_|ios_)/);
  });

  test('regenerateDeviceNo should create new device number', () => {
    const deviceNo1 = getDeviceNo();
    const deviceNo2 = regenerateDeviceNo();
    const deviceNo3 = getDeviceNo();

    // 重新生成的设备号应该不同
    expect(deviceNo1).not.toBe(deviceNo2);

    // 重新生成后，getDeviceNo应该返回新的设备号
    expect(deviceNo2).toBe(deviceNo3);
  });

  test('device platform detection should work correctly', () => {
    // 生成设备号后应该能检测到平台类型
    const deviceNo = getDeviceNo();
    const platform = getDevicePlatform();

    expect(['android', 'ios', 'unknown']).toContain(platform);

    if (deviceNo.startsWith('android_')) {
      expect(platform).toBe('android');
    } else if (deviceNo.startsWith('ios_')) {
      expect(platform).toBe('ios');
    }
  });

  test('device number validation should work correctly', () => {
    // 测试有效的Android设备号
    expect(validateDeviceNo('android_1234567890abcdef')).toBe(true);

    // 测试有效的iOS设备号 (注意：必须是大写十六进制字符)
    expect(validateDeviceNo('ios_12345678-ABCD-1234-ABCD-123456789012')).toBe(true);

    // 测试无效的设备号
    expect(validateDeviceNo('')).toBe(false);
    expect(validateDeviceNo('invalid_device')).toBe(false);
    expect(validateDeviceNo('android_123')).toBe(false); // 长度不够
    expect(validateDeviceNo('ios_invalid-format')).toBe(false);
    expect(validateDeviceNo('ios_12345678-abcd-1234-abcd-123456789012')).toBe(false); // 小写字母
  });
});
