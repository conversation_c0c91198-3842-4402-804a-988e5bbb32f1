/**
 * 基础API测试
 */

import { buildApplyPrivateKeyRequest } from '../app/api/custom-api/base-api';

describe('Base API Tests', () => {
  test('buildApplyPrivateKeyRequest should generate correct request parameters', () => {
    const deviceNo = 'TEST_DEVICE_001';
    
    try {
      const request = buildApplyPrivateKeyRequest(deviceNo);
      
      // 验证返回的对象结构
      expect(request).toHaveProperty('deviceNo');
      expect(request).toHaveProperty('masterKey');
      expect(request).toHaveProperty('sign');
      
      // 验证deviceNo正确传递
      expect(request.deviceNo).toBe(deviceNo);
      
      // 验证masterKey以04开头（根据步骤4的要求）
      expect(request.masterKey).toMatch(/^04/);
      
      // 验证sign不为空
      expect(request.sign).toBeTruthy();
      expect(typeof request.sign).toBe('string');
      
      console.log('Generated request:', JSON.stringify(request, null, 2));
      
    } catch (error) {
      console.error('Test failed with error:', error);
      throw error;
    }
  });
  
  test('buildApplyPrivateKeyRequest should generate different signs for different deviceNo', () => {
    const deviceNo1 = 'DEVICE_001';
    const deviceNo2 = 'DEVICE_002';
    
    const request1 = buildApplyPrivateKeyRequest(deviceNo1);
    const request2 = buildApplyPrivateKeyRequest(deviceNo2);
    
    // 不同的deviceNo应该生成不同的sign
    expect(request1.sign).not.toBe(request2.sign);
    
    // 但masterKey应该相同（因为使用相同的MASTER_KEY）
    expect(request1.masterKey).toBe(request2.masterKey);
  });
  
  test('buildApplyPrivateKeyRequest should be consistent for same deviceNo', () => {
    const deviceNo = 'CONSISTENT_DEVICE';
    
    const request1 = buildApplyPrivateKeyRequest(deviceNo);
    const request2 = buildApplyPrivateKeyRequest(deviceNo);
    
    // 相同的deviceNo应该生成相同的结果
    expect(request1.deviceNo).toBe(request2.deviceNo);
    expect(request1.masterKey).toBe(request2.masterKey);
    expect(request1.sign).toBe(request2.sign);
  });
});
