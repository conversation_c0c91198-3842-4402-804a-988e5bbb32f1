"use client";

import { useState } from "react";
import { useNavigate } from "react-router-dom";
import styles from "./login.module.scss";
import { IconButton } from "./button";
import { PasswordInput } from "./ui-lib";
import { Path } from "../constant";
import Locale from "../locales";
import LeftIcon from "../icons/left.svg";
import DeepSeekIcon from "../icons/llm-icons/deepseek.svg";
import EyeIcon from "../icons/eye.svg";
import EyeOffIcon from "../icons/eye-off.svg";
// 移除图标导入，使用文字标识
import { useMobileScreen } from "../utils";
import { API, LoginRequest } from "../api/custom-api";
import clsx from "clsx";

export function LoginPage() {
  const isMobile = useMobileScreen();
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    account: "",
    password: "",
    type: 0,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [showPassword, setShowPassword] = useState(false);

  const handleInputChange = (field: keyof LoginRequest, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (error) setError(""); // Clear error when user starts typing
  };

  const validateForm = (): boolean => {
    if (!formData.account.trim()) {
      setError("请输入账号");
      return false;
    }
    if (!formData.password.trim()) {
      setError("请输入密码");
      return false;
    }
    return true;
  };

  const handleLogin = async () => {
    if (!validateForm()) return;

    setLoading(true);
    setError("");

    try {
      const requestData: LoginRequest = {
        account: formData.account.trim(),
        password: formData.password,
        type: formData.type,
      };

      await API.auth.login(requestData);

      navigate(Path.Home);
    } catch (err: any) {
      console.error("Login error:", err);
      setError(err.message || "网络错误，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !loading) {
      handleLogin();
    }
  };

  return (
    <div className={styles["login-page"]}>
      <div className={styles["login-container"]}>
        {/* Header */}
        <div className={styles["login-header"]}>
          <IconButton
            icon={<LeftIcon />}
            text="返回"
            onClick={() => navigate(Path.Home)}
            className={styles["back-button"]}
          />
        </div>

        {/* Logo and Title */}
        <div className={styles["login-brand"]}>
          {/* <div className={clsx("no-dark", styles["login-logo"])}>
            <DeepSeekIcon />
          </div> */}
          <h1 className={styles["login-title"]}>欢迎回来</h1>
          <p className={styles["login-subtitle"]}>登录您的账户继续使用</p>
        </div>

        {/* Login Form */}
        <div className={styles["login-form"]}>
          {error && <div className={styles["error-message"]}>{error}</div>}

          <div className={styles["form-group"]}>
            <div className={styles["input-wrapper"]}>
              <span className={styles["input-icon"]}>📧</span>
              <input
                type="text"
                className={styles["form-input"]}
                placeholder="输入您的账号"
                value={formData.account}
                onChange={(e) => handleInputChange("account", e.target.value)}
                onKeyPress={handleKeyPress}
                disabled={loading}
              />
            </div>
          </div>

          <div className={styles["form-group"]}>
            <div className={styles["input-wrapper"]}>
              <span className={styles["input-icon"]}>🔒</span>
              <div className={styles["password-input-wrapper"]}>
                <input
                  type={showPassword ? "text" : "password"}
                  className={styles["form-input"]}
                  placeholder="输入您的密码"
                  value={formData.password}
                  onChange={(e) =>
                    handleInputChange("password", e.target.value)
                  }
                  onKeyPress={handleKeyPress}
                  disabled={loading}
                />
                <button
                  type="button"
                  className={styles["password-toggle"]}
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={loading}
                >
                  {showPassword ? "🙈" : "👁️"}
                </button>
              </div>
            </div>
          </div>

          <div className={styles["form-actions"]}>
            <button
              className={clsx(styles["login-button"], {
                [styles["loading"]]: loading,
              })}
              onClick={handleLogin}
              disabled={loading}
            >
              {loading ? "登录中..." : "登录"}
            </button>
          </div>

          <div className={styles["form-footer"]}>
            <p className={styles["register-link"]}>
              还没有账户？
              <button
                type="button"
                className={styles["link-button"]}
                onClick={() => navigate(Path.Register)}
                disabled={loading}
              >
                立即注册
              </button>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
