/**
 * 登录页面组件
 * 集成设备私钥申请和管理功能
 */

import React, { useState } from 'react';
import { useLoginSecurity } from '../hooks/useLoginSecurity';

interface LoginPageProps {
  onLogin?: (credentials: { username: string; password: string }) => void;
}

export function LoginPage({ onLogin }: LoginPageProps) {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoggingIn, setIsLoggingIn] = useState(false);

  // 使用登录安全Hook
  const {
    isInitializing,
    isInitialized,
    deviceNo,
    privateKey,
    success: securitySuccess,
    error: securityError,
    reinitialize,
    isSecurityReady,
    hasError
  } = useLoginSecurity();

  /**
   * 处理登录
   */
  const handleLogin = async () => {
    if (!isSecurityReady) {
      alert('设备安全初始化未完成，请稍后再试');
      return;
    }

    if (!username || !password) {
      alert('请输入用户名和密码');
      return;
    }

    setIsLoggingIn(true);
    try {
      // 这里可以使用privateKey进行加密等操作
      console.log('使用设备私钥进行登录:', {
        deviceNo,
        privateKeyLength: privateKey?.length,
        username,
        // password 不应该打印到控制台
      });

      // 调用登录回调
      if (onLogin) {
        await onLogin({ username, password });
      }
    } catch (error) {
      console.error('登录失败:', error);
      alert('登录失败，请重试');
    } finally {
      setIsLoggingIn(false);
    }
  };

  /**
   * 重新初始化安全设置
   */
  const handleReinitialize = async () => {
    try {
      await reinitialize();
    } catch (error) {
      console.error('重新初始化失败:', error);
    }
  };

  return (
    <div style={{ 
      maxWidth: '400px', 
      margin: '50px auto', 
      padding: '20px', 
      border: '1px solid #ddd', 
      borderRadius: '8px',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h2 style={{ textAlign: 'center', marginBottom: '30px' }}>用户登录</h2>

      {/* 安全状态显示 */}
      <div style={{ 
        marginBottom: '20px', 
        padding: '10px', 
        backgroundColor: isSecurityReady ? '#e8f5e8' : hasError ? '#ffe8e8' : '#f0f0f0',
        borderRadius: '4px',
        fontSize: '14px'
      }}>
        <div><strong>安全状态:</strong></div>
        {isInitializing && <div>🔄 正在初始化设备安全...</div>}
        {isInitialized && securitySuccess && (
          <div>
            <div>✅ 设备安全初始化成功</div>
            <div>📱 设备号: {deviceNo}</div>
            <div>🔐 私钥长度: {privateKey?.length || 0} 字符</div>
          </div>
        )}
        {hasError && (
          <div>
            <div>❌ 安全初始化失败: {securityError}</div>
            <button 
              onClick={handleReinitialize}
              style={{ 
                marginTop: '5px', 
                padding: '5px 10px', 
                fontSize: '12px',
                backgroundColor: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '3px',
                cursor: 'pointer'
              }}
            >
              重新初始化
            </button>
          </div>
        )}
      </div>

      {/* 登录表单 */}
      <div style={{ marginBottom: '15px' }}>
        <label style={{ display: 'block', marginBottom: '5px' }}>用户名:</label>
        <input
          type="text"
          value={username}
          onChange={(e) => setUsername(e.target.value)}
          style={{
            width: '100%',
            padding: '8px',
            border: '1px solid #ddd',
            borderRadius: '4px',
            fontSize: '14px'
          }}
          placeholder="请输入用户名"
        />
      </div>

      <div style={{ marginBottom: '20px' }}>
        <label style={{ display: 'block', marginBottom: '5px' }}>密码:</label>
        <input
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          style={{
            width: '100%',
            padding: '8px',
            border: '1px solid #ddd',
            borderRadius: '4px',
            fontSize: '14px'
          }}
          placeholder="请输入密码"
        />
      </div>

      <button
        onClick={handleLogin}
        disabled={isLoggingIn || !isSecurityReady}
        style={{
          width: '100%',
          padding: '12px',
          backgroundColor: isSecurityReady ? '#007bff' : '#ccc',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          fontSize: '16px',
          cursor: isSecurityReady ? 'pointer' : 'not-allowed',
          opacity: isLoggingIn ? 0.7 : 1
        }}
      >
        {isLoggingIn ? '登录中...' : isSecurityReady ? '登录' : '等待安全初始化...'}
      </button>

      {/* 调试信息（开发环境） */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{ 
          marginTop: '20px', 
          padding: '10px', 
          backgroundColor: '#f8f9fa', 
          borderRadius: '4px',
          fontSize: '12px',
          color: '#666'
        }}>
          <div><strong>调试信息:</strong></div>
          <div>初始化状态: {isInitialized ? '已完成' : '未完成'}</div>
          <div>安全就绪: {isSecurityReady ? '是' : '否'}</div>
          <div>设备号: {deviceNo || '未获取'}</div>
          <div>私钥状态: {privateKey ? '已获取' : '未获取'}</div>
        </div>
      )}
    </div>
  );
}

export default LoginPage;
