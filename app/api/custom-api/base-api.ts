/**
 * 基础模块API接口
 * 实现设备私钥申请功能，包含SM2/SM4加密处理
 */

import { apiClient } from "../../utils/api-client";
import { getDeviceNo } from "../../utils/device";

// ==================== 常量定义 ====================

/**
 * SM2公钥 - 用于加密MASTER_KEY
 * 注意：这是公钥，不是私钥，可以安全地存储在客户端代码中
 */
const SM2_PUBLIC_KEY =
  "0457D439E67908DC998F9B25D51C4D499A031CEACA885FE8E1FF2EA620B9BE6E64F83EAF5F04AA1032DB6C27268586BADDEC3FF6858DE1D6CA750BC8AB54CA5889";

/**
 * 主密钥 - 需要进行加密处理的原始密钥
 * 在实际应用中，这个值可能来自配置文件或环境变量
 */
const MASTER_KEY = 

/**
 * SM2加密模式：C1C3C2
 */
const SM2_CIPHER_MODE = 1;

// ==================== 类型定义 ====================

/**
 * 申请私钥请求参数接口
 */
export interface ApplyPrivateKeyRequest {
  /** 设备号 */
  deviceNo: string;
  /** 加密后的主密钥（以04开头） */
  masterKey: string;
  /** 设备号的SM4加密签名 */
  sign: string;
}

/**
 * SM2加密接口
 */
interface SM2Interface {
  doEncrypt(msgString: string, publicKey: string, cipherMode?: number): string;
}

/**
 * SM4加密接口
 */
interface SM4Interface {
  encrypt(inData: string, key: string, options?: any): string;
}

// ==================== 加密库加载 ====================

/**
 * 加载SM加密库
 */
function loadSMCrypto(): { sm2: SM2Interface; sm4: SM4Interface } | any {
  try {
    // 生产环境使用真实的sm-crypto库
    const smCrypto = require("sm-crypto");
    return {
      sm2: smCrypto.sm2,
      sm4: smCrypto.sm4,
    };
  } catch (error) {
    console.warn("Failed to load sm-crypto, using mock implementation:", error);
  }
}

// 初始化加密库
const { sm2, sm4 } = loadSMCrypto();

// ==================== 核心加密处理函数 ====================

/**
 * 处理主密钥的完整加密流程
 *
 * 加密步骤：
 * 1. 对MASTER_KEY进行base64编码
 * 2. 使用SM2公钥加密base64后的数据
 * 3. 使用C1C3C2模式处理加密结果
 * 4. 在结果前添加"04"前缀
 *
 * @returns 处理后的masterKey字符串
 * @throws {Error} 当加密处理失败时抛出错误
 */
function processMasterKey(): string {
  try {
    // 步骤1: 对MASTER_KEY进行base64编码
    const base64MasterKey = btoa(MASTER_KEY);
    console.log(
      `[Crypto] Base64 encoded master key length: ${base64MasterKey.length}`,
    );

    // 步骤2-3: 使用SM2加密，采用C1C3C2模式
    // 注意：这里直接使用SM2_PUBLIC_KEY作为加密公钥
    const encryptedData = sm2.doEncrypt(
      base64MasterKey,
      SM2_PUBLIC_KEY,
      SM2_CIPHER_MODE,
    );
    console.log(`[Crypto] SM2 encrypted data length: ${encryptedData.length}`);

    // 步骤4: 添加"04"前缀
    const finalMasterKey = "04" + encryptedData;
    console.log(`[Crypto] Final master key length: ${finalMasterKey.length}`);

    return finalMasterKey;
  } catch (error) {
    console.error("[Crypto] Failed to process master key:", error);
    throw new Error(
      `主密钥处理失败: ${
        error instanceof Error ? error.message : String(error)
      }`,
    );
  }
}

/**
 * 生成设备号的SM4加密签名
 *
 * @param deviceNo 设备号
 * @param encryptionKey 用于SM4加密的密钥
 * @returns SM4加密后的签名字符串
 * @throws {Error} 当签名生成失败时抛出错误
 */
function generateDeviceSign(deviceNo: string, encryptionKey: string): string {
  try {
    // 使用主密钥的前32位作为SM4加密密钥
    // SM4要求密钥长度为128位（32个十六进制字符）
    const sm4Key = SM2_PUBLIC_KEY.substring(0, 32);
    console.log(`[Crypto] Using SM4 key length: ${sm4Key.length}`);

    // 使用SM4加密设备号
    const encryptedSign = sm4.encrypt(deviceNo, sm4Key);
    console.log(`[Crypto] Generated sign length: ${encryptedSign.length}`);

    return encryptedSign;
  } catch (error) {
    console.error("[Crypto] Failed to generate device sign:", error);
    throw new Error(
      `设备签名生成失败: ${
        error instanceof Error ? error.message : String(error)
      }`,
    );
  }
}

// ==================== 公共API接口 ====================

/**
 * 组装申请私钥的请求参数
 *
 * 完整的处理流程：
 * 1. 获取或生成设备号
 * 2. 处理主密钥（base64编码 -> SM2加密 -> 添加04前缀）
 * 3. 生成设备签名（SM4加密设备号）
 *
 * @param deviceNo 设备号（可选，如果不提供则自动生成）
 * @returns 完整的API请求参数
 * @throws {Error} 当参数组装失败时抛出错误
 */
export function buildApplyPrivateKeyRequest(
  deviceNo?: string,
): ApplyPrivateKeyRequest {
  try {
    // 步骤1: 获取设备号（如果没有提供则自动获取/生成）
    const finalDeviceNo = deviceNo || getDeviceNo();
    console.log(`[API] Using device number: ${finalDeviceNo}`);

    // 步骤2: 处理主密钥
    const processedMasterKey = processMasterKey();

    // 步骤3: 生成设备签名
    const sign = generateDeviceSign(finalDeviceNo, MASTER_KEY);

    const request: ApplyPrivateKeyRequest = {
      deviceNo: finalDeviceNo,
      masterKey: processedMasterKey,
      sign,
    };

    console.log(`[API] Request parameters generated successfully:`, {
      deviceNo: request.deviceNo,
      masterKeyLength: request.masterKey.length,
      signLength: request.sign.length,
      masterKeyPrefix: request.masterKey.substring(0, 10) + "...",
    });

    return request;
  } catch (error) {
    console.error("[API] Failed to build request parameters:", error);
    throw new Error(
      `请求参数组装失败: ${
        error instanceof Error ? error.message : String(error)
      }`,
    );
  }
}

/**
 * 申请私钥API调用
 *
 * 自动处理设备号生成、参数加密和API请求
 *
 * @param deviceNo 设备号（可选，如果不提供则自动生成）
 * @returns API响应结果
 * @throws {Error} 当API调用失败时抛出错误
 */
export async function applyPrivateKey(deviceNo?: string): Promise<any> {
  try {
    console.log(`[API] Starting private key application process...`);

    // 组装请求参数
    const request = buildApplyPrivateKeyRequest(deviceNo);

    // 发送API请求
    console.log(`[API] Sending request to /device/applyPrivateKey`);
    const result = await apiClient.post("/device/applyPrivateKey", request);

    console.log(`[API] Private key application successful`);
    return result;
  } catch (error) {
    console.error("[API] Private key application failed:", error);
    throw new Error(
      `私钥申请失败: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}
