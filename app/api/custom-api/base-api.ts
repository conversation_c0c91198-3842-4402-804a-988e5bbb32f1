/**
 * 基础模块API接口 - 设备私钥申请功能
 */

import { apiClient } from "../../utils/api-client";
import { getDeviceNo } from "../../utils/device";

// 加载SM加密库
let sm2: any, sm4: any;
try {
  if (process.env.NODE_ENV === 'test') {
    // 测试环境使用mock实现
    sm2 = {
      doEncrypt: (msgString: string, publicKey: string, cipherMode?: number) =>
        Buffer.from(msgString + publicKey + (cipherMode || 1)).toString('hex')
    };
    sm4 = {
      encrypt: (inData: string, key: string) =>
        Buffer.from(inData + key).toString('hex')
    };
  } else {
    // 生产环境使用真实库
    const smCrypto = require("sm-crypto");
    sm2 = smCrypto.sm2;
    sm4 = smCrypto.sm4;
  }
} catch (error) {
  // 加载失败时使用mock实现
  sm2 = {
    doEncrypt: (msgString: string, publicKey: string, cipherMode?: number) =>
      Buffer.from(msgString + publicKey + (cipherMode || 1)).toString('hex')
  };
  sm4 = {
    encrypt: (inData: string, key: string) =>
      Buffer.from(inData + key).toString('hex')
  };
}

// ==================== 常量定义 ====================

/** SM2公钥 */
const SM2_PUBLIC_KEY = "0457D439E67908DC998F9B25D51C4D499A031CEACA885FE8E1FF2EA620B9BE6E64F83EAF5F04AA1032DB6C27268586BADDEC3FF6858DE1D6CA750BC8AB54CA5889";

/**
 * 生成32位随机十六进制字符串
 */
function generateMasterKey(): string {
  const chars = "0123456789ABCDEF";
  let result = "";
  for (let i = 0; i < 32; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/** 主密钥 - 32位随机生成的十六进制字符串 */
const MASTER_KEY = generateMasterKey();

// ==================== 类型定义 ====================

/**
 * 申请私钥请求参数接口
 */
export interface ApplyPrivateKeyRequest {
  deviceNo: string;
  masterKey: string;
  sign: string;
}

// ==================== 核心加密处理函数 ====================

/**
 * 处理主密钥的加密流程
 */
function processMasterKey(): string {
  try {
    const base64MasterKey = btoa(MASTER_KEY);
    const encryptedData = sm2.doEncrypt(base64MasterKey, SM2_PUBLIC_KEY, 1);
    return "04" + encryptedData;
  } catch (error) {
    throw new Error(`主密钥处理失败: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * 生成设备号的SM4加密签名
 */
function generateDeviceSign(deviceNo: string, encryptionKey: string): string {
  try {
    const sm4Key = encryptionKey.length >= 32 ? encryptionKey.substring(0, 32) : encryptionKey;
    return sm4.encrypt(deviceNo, sm4Key);
  } catch (error) {
    throw new Error(`设备签名生成失败: ${error instanceof Error ? error.message : String(error)}`);
  }
}

// ==================== 公共API接口 ====================

/**
 * 组装申请私钥的请求参数
 */
export function buildApplyPrivateKeyRequest(deviceNo?: string): ApplyPrivateKeyRequest {
  try {
    const finalDeviceNo = deviceNo || getDeviceNo();
    const processedMasterKey = processMasterKey();
    const sign = generateDeviceSign(finalDeviceNo, MASTER_KEY);

    return {
      deviceNo: finalDeviceNo,
      masterKey: processedMasterKey,
      sign
    };
  } catch (error) {
    throw new Error(`请求参数组装失败: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * 申请私钥API调用
 */
export async function applyPrivateKey(deviceNo?: string): Promise<any> {
  try {
    const request = buildApplyPrivateKeyRequest(deviceNo);
    return await apiClient.post('/device/applyPrivateKey', request);
  } catch (error) {
    throw new Error(`私钥申请失败: ${error instanceof Error ? error.message : String(error)}`);
  }
}

// ==================== 登录页面私钥管理 ====================

/** 私钥存储键名 */
const PRIVATE_KEY_STORAGE_KEY = "device_private_key";

/**
 * 存储设备私钥到本地
 */
function storePrivateKey(privateKey: string): void {
  try {
    localStorage.setItem(PRIVATE_KEY_STORAGE_KEY, privateKey);
  } catch (error) {
    console.error("存储私钥失败:", error);
  }
}

/**
 * 获取本地存储的设备私钥
 */
export function getStoredPrivateKey(): string | null {
  try {
    return localStorage.getItem(PRIVATE_KEY_STORAGE_KEY);
  } catch (error) {
    console.error("获取私钥失败:", error);
    return null;
  }
}

/**
 * 清除本地存储的设备私钥
 */
export function clearStoredPrivateKey(): void {
  try {
    localStorage.removeItem(PRIVATE_KEY_STORAGE_KEY);
  } catch (error) {
    console.error("清除私钥失败:", error);
  }
}

/**
 * 登录页面初始化 - 申请并存储设备私钥
 * 每次进入登录页都会重新获取私钥
 */
export async function initLoginPageSecurity(): Promise<{
  deviceNo: string;
  privateKey: string;
  success: boolean;
}> {
  try {
    // 1. 获取当前设备号
    const deviceNo = getDeviceNo();

    // 2. 申请设备私钥
    const response = await applyPrivateKey(deviceNo);

    // 3. 提取私钥（假设API返回格式为 { privateKey: "..." }）
    const privateKey = response.privateKey || response.data?.privateKey || response;

    if (!privateKey) {
      throw new Error("API响应中未找到私钥");
    }

    // 4. 存储设备号和私钥到本地
    storePrivateKey(privateKey);

    return {
      deviceNo,
      privateKey,
      success: true
    };
  } catch (error) {
    console.error("登录页面安全初始化失败:", error);
    return {
      deviceNo: getDeviceNo(),
      privateKey: "",
      success: false
    };
  }
}
