/**
 * 使用示例：如何使用设备号生成和API调用功能
 */

import { applyPrivateKey, buildApplyPrivateKeyRequest } from './base-api';
import { getDeviceNo, regenerateDeviceNo, clearDeviceNo } from '../../utils/device';

/**
 * 示例1：基本使用 - 自动生成设备号并申请私钥
 */
export async function example1_BasicUsage() {
  console.log('=== 示例1：基本使用 ===');
  
  try {
    // 直接调用API，会自动生成设备号
    const result = await applyPrivateKey();
    console.log('申请私钥成功:', result);
    return result;
  } catch (error) {
    console.error('申请私钥失败:', error);
    throw error;
  }
}

/**
 * 示例2：手动管理设备号
 */
export async function example2_ManualDeviceManagement() {
  console.log('=== 示例2：手动管理设备号 ===');
  
  // 检查当前设备号
  let currentDeviceNo = getDeviceNo();
  console.log('当前设备号:', currentDeviceNo);
  
  // 生成请求参数（不实际发送请求）
  const requestParams = buildApplyPrivateKeyRequest();
  console.log('生成的请求参数:', {
    deviceNo: requestParams.deviceNo,
    masterKeyLength: requestParams.masterKey.length,
    signLength: requestParams.sign.length
  });
  
  // 重新生成设备号
  const newDeviceNo = regenerateDeviceNo();
  console.log('重新生成的设备号:', newDeviceNo);
  
  // 使用新设备号生成请求参数
  const newRequestParams = buildApplyPrivateKeyRequest();
  console.log('使用新设备号的请求参数:', {
    deviceNo: newRequestParams.deviceNo,
    masterKeyLength: newRequestParams.masterKey.length,
    signLength: newRequestParams.sign.length
  });
  
  return {
    oldDeviceNo: currentDeviceNo,
    newDeviceNo: newDeviceNo,
    oldRequest: requestParams,
    newRequest: newRequestParams
  };
}

/**
 * 示例3：指定设备号
 */
export async function example3_SpecificDeviceNo() {
  console.log('=== 示例3：指定设备号 ===');
  
  const customDeviceNo = 'custom_device_12345';
  
  try {
    // 使用指定的设备号申请私钥
    const result = await applyPrivateKey(customDeviceNo);
    console.log('使用指定设备号申请私钥成功:', result);
    return result;
  } catch (error) {
    console.error('使用指定设备号申请私钥失败:', error);
    throw error;
  }
}

/**
 * 示例4：批量处理不同设备号
 */
export async function example4_BatchProcessing() {
  console.log('=== 示例4：批量处理不同设备号 ===');
  
  const deviceNumbers = [
    'batch_device_001',
    'batch_device_002',
    'batch_device_003'
  ];
  
  const results = [];
  
  for (const deviceNo of deviceNumbers) {
    try {
      console.log(`处理设备号: ${deviceNo}`);
      
      // 生成请求参数
      const requestParams = buildApplyPrivateKeyRequest(deviceNo);
      
      // 这里可以发送实际的API请求
      // const apiResult = await applyPrivateKey(deviceNo);
      
      results.push({
        deviceNo: deviceNo,
        requestParams: requestParams,
        status: 'success'
      });
      
      console.log(`设备号 ${deviceNo} 处理完成`);
    } catch (error) {
      console.error(`设备号 ${deviceNo} 处理失败:`, error);
      results.push({
        deviceNo: deviceNo,
        error: error.message,
        status: 'failed'
      });
    }
  }
  
  return results;
}

/**
 * 示例5：错误处理和重试机制
 */
export async function example5_ErrorHandlingAndRetry() {
  console.log('=== 示例5：错误处理和重试机制 ===');
  
  const maxRetries = 3;
  let retryCount = 0;
  
  while (retryCount < maxRetries) {
    try {
      console.log(`尝试申请私钥 (第${retryCount + 1}次)`);
      
      const result = await applyPrivateKey();
      console.log('申请私钥成功:', result);
      return result;
      
    } catch (error) {
      retryCount++;
      console.error(`第${retryCount}次尝试失败:`, error);
      
      if (retryCount >= maxRetries) {
        console.error('达到最大重试次数，申请失败');
        throw new Error(`申请私钥失败，已重试${maxRetries}次: ${error.message}`);
      }
      
      // 重试前重新生成设备号
      const newDeviceNo = regenerateDeviceNo();
      console.log(`重新生成设备号进行重试: ${newDeviceNo}`);
      
      // 等待一段时间再重试
      await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
    }
  }
}

/**
 * 示例6：清理和重置
 */
export function example6_CleanupAndReset() {
  console.log('=== 示例6：清理和重置 ===');
  
  // 获取当前设备号
  const currentDeviceNo = getDeviceNo();
  console.log('清理前的设备号:', currentDeviceNo);
  
  // 清除设备号
  clearDeviceNo();
  console.log('设备号已清除');
  
  // 重新获取设备号（会自动生成新的）
  const newDeviceNo = getDeviceNo();
  console.log('清理后重新生成的设备号:', newDeviceNo);
  
  return {
    oldDeviceNo: currentDeviceNo,
    newDeviceNo: newDeviceNo
  };
}

/**
 * 运行所有示例
 */
export async function runAllExamples() {
  console.log('开始运行所有示例...\n');
  
  try {
    // 注意：示例1和3会实际发送API请求，在没有真实API服务器的情况下会失败
    // 这里我们只运行不发送请求的示例
    
    await example2_ManualDeviceManagement();
    console.log('\n');
    
    await example4_BatchProcessing();
    console.log('\n');
    
    example6_CleanupAndReset();
    console.log('\n');
    
    console.log('所有示例运行完成！');
  } catch (error) {
    console.error('运行示例时出错:', error);
  }
}
