/**
 * 登录页面安全管理Hook
 * 负责设备私钥的申请、存储和管理
 */

import { useState, useEffect } from 'react';
import { initLoginPageSecurity, getStoredPrivateKey, clearStoredPrivateKey } from '../api/custom-api/base-api';
import { getDeviceNo } from '../utils/device';

interface LoginSecurityState {
  /** 是否正在初始化 */
  isInitializing: boolean;
  /** 初始化是否完成 */
  isInitialized: boolean;
  /** 设备号 */
  deviceNo: string;
  /** 设备私钥 */
  privateKey: string | null;
  /** 初始化是否成功 */
  success: boolean;
  /** 错误信息 */
  error: string | null;
}

/**
 * 登录页面安全管理Hook
 * 
 * 功能：
 * 1. 页面加载时自动申请设备私钥
 * 2. 将设备号和私钥存储到本地
 * 3. 每次进入都重新获取私钥
 * 4. 提供私钥状态管理
 */
export function useLoginSecurity() {
  const [state, setState] = useState<LoginSecurityState>({
    isInitializing: false,
    isInitialized: false,
    deviceNo: '',
    privateKey: null,
    success: false,
    error: null
  });

  /**
   * 初始化安全设置
   */
  const initializeSecurity = async () => {
    setState(prev => ({
      ...prev,
      isInitializing: true,
      error: null
    }));

    try {
      // 清除之前的私钥（每次都重新获取）
      clearStoredPrivateKey();
      
      // 申请新的私钥
      const result = await initLoginPageSecurity();
      
      setState(prev => ({
        ...prev,
        isInitializing: false,
        isInitialized: true,
        deviceNo: result.deviceNo,
        privateKey: result.privateKey,
        success: result.success,
        error: result.success ? null : '私钥申请失败'
      }));

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      setState(prev => ({
        ...prev,
        isInitializing: false,
        isInitialized: true,
        success: false,
        error: errorMessage
      }));
      
      throw error;
    }
  };

  /**
   * 重新初始化（手动触发）
   */
  const reinitialize = async () => {
    return await initializeSecurity();
  };

  /**
   * 获取当前私钥
   */
  const getCurrentPrivateKey = (): string | null => {
    return state.privateKey || getStoredPrivateKey();
  };

  /**
   * 获取当前设备号
   */
  const getCurrentDeviceNo = (): string => {
    return state.deviceNo || getDeviceNo();
  };

  /**
   * 检查是否已准备就绪
   */
  const isReady = (): boolean => {
    return state.isInitialized && state.success && !!state.privateKey;
  };

  // 组件挂载时自动初始化
  useEffect(() => {
    initializeSecurity();
  }, []);

  return {
    // 状态
    isInitializing: state.isInitializing,
    isInitialized: state.isInitialized,
    deviceNo: state.deviceNo,
    privateKey: state.privateKey,
    success: state.success,
    error: state.error,
    
    // 方法
    reinitialize,
    getCurrentPrivateKey,
    getCurrentDeviceNo,
    isReady,
    
    // 便捷状态
    isLoading: state.isInitializing,
    hasError: !!state.error,
    isSecurityReady: isReady()
  };
}

/**
 * 用于其他页面获取私钥的简化Hook
 */
export function useDeviceSecurity() {
  const [privateKey, setPrivateKey] = useState<string | null>(null);
  const [deviceNo, setDeviceNo] = useState<string>('');

  useEffect(() => {
    // 获取存储的私钥和设备号
    const storedPrivateKey = getStoredPrivateKey();
    const currentDeviceNo = getDeviceNo();
    
    setPrivateKey(storedPrivateKey);
    setDeviceNo(currentDeviceNo);
  }, []);

  return {
    privateKey,
    deviceNo,
    hasPrivateKey: !!privateKey,
    isSecurityReady: !!privateKey && !!deviceNo
  };
}
