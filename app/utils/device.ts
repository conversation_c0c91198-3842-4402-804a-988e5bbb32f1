/**
 * 设备号管理工具
 *
 * 提供设备号的自动生成、本地存储和管理功能
 * 支持Android和iOS两种设备号格式的自动识别和生成
 */

import { safeLocalStorage } from "../utils";

// ==================== 常量定义 ====================

/** 本地存储中设备号的键名 */
const DEVICE_NO_KEY = "device_no";

/** 安全的localStorage实例 */
const localStorage = safeLocalStorage();

// ==================== 类型定义 ====================

/** 支持的平台类型 */
type PlatformType = "android" | "ios" | "unknown";

// ==================== 私有工具函数 ====================

/**
 * 生成随机的Android设备号
 *
 * 格式: android_xxxxxxxxxxxxxxxx (16位随机十六进制字符)
 * 示例: android_a1b2c3d4e5f67890
 *
 * @returns Android格式的设备号
 */
function generateAndroidDeviceNo(): string {
  const chars = "0123456789abcdef";
  let result = "android_";

  for (let i = 0; i < 16; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }

  console.log(`[Device] Generated Android device number: ${result}`);
  return result;
}

/**
 * 生成随机的iOS设备号
 *
 * 格式: ios_XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX (UUID格式)
 * 示例: ios_A1B2C3D4-E5F6-7890-ABCD-EF1234567890
 *
 * @returns iOS格式的设备号
 */
function generateIOSDeviceNo(): string {
  const chars = "0123456789ABCDEF";
  const sections = [8, 4, 4, 4, 12];
  let result = "ios_";

  for (let i = 0; i < sections.length; i++) {
    if (i > 0) result += "-";
    for (let j = 0; j < sections[i]; j++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
  }

  console.log(`[Device] Generated iOS device number: ${result}`);
  return result;
}

/**
 * 检测当前运行平台类型
 *
 * 通过User-Agent字符串识别设备类型
 *
 * @returns 平台类型
 */
function detectPlatform(): PlatformType {
  if (typeof navigator === "undefined") {
    console.log("[Device] Navigator not available, defaulting to unknown platform");
    return "unknown";
  }

  const userAgent = navigator.userAgent.toLowerCase();
  console.log(`[Device] Detecting platform from User-Agent: ${userAgent}`);

  if (/android/.test(userAgent)) {
    console.log("[Device] Detected Android platform");
    return "android";
  } else if (/iphone|ipad|ipod/.test(userAgent)) {
    console.log("[Device] Detected iOS platform");
    return "ios";
  }

  console.log("[Device] Unknown platform detected");
  return "unknown";
}

/**
 * 根据平台类型生成对应格式的设备号
 *
 * 优先级：iOS > Android > 默认Android
 *
 * @returns 生成的设备号
 */
function generateDeviceNo(): string {
  const platform = detectPlatform();

  switch (platform) {
    case "ios":
      return generateIOSDeviceNo();
    case "android":
    case "unknown":
    default:
      return generateAndroidDeviceNo();
  }
}

// ==================== 公共API接口 ====================

/**
 * 获取设备号
 *
 * 如果本地存储中已有设备号则直接返回，否则生成新的设备号并存储
 * 这是获取设备号的主要入口函数
 *
 * @returns 设备号字符串
 * @throws {Error} 当存储操作失败时，会返回临时生成的设备号而不抛出错误
 */
export function getDeviceNo(): string {
  try {
    // 尝试从本地存储获取现有设备号
    let deviceNo = localStorage.getItem(DEVICE_NO_KEY);

    if (!deviceNo) {
      // 本地没有设备号，生成新的
      deviceNo = generateDeviceNo();

      // 存储到本地
      localStorage.setItem(DEVICE_NO_KEY, deviceNo);
      console.log("[Device] Generated new device number:", deviceNo);
    } else {
      console.log("[Device] Retrieved existing device number:", deviceNo);
    }

    return deviceNo;
  } catch (error) {
    console.error("[Device] Error getting device number:", error);

    // 如果存储操作失败，返回临时生成的设备号
    const tempDeviceNo = generateDeviceNo();
    console.warn("[Device] Using temporary device number:", tempDeviceNo);
    return tempDeviceNo;
  }
}

/**
 * 重新生成设备号
 *
 * 强制生成新的设备号并覆盖本地存储中的现有设备号
 * 适用于需要更换设备标识的场景
 *
 * @returns 新生成的设备号
 * @throws {Error} 当存储操作失败时，会返回临时生成的设备号而不抛出错误
 */
export function regenerateDeviceNo(): string {
  try {
    const newDeviceNo = generateDeviceNo();
    localStorage.setItem(DEVICE_NO_KEY, newDeviceNo);
    console.log("[Device] Regenerated device number:", newDeviceNo);
    return newDeviceNo;
  } catch (error) {
    console.error("[Device] Error regenerating device number:", error);

    // 如果存储失败，返回临时生成的设备号
    const tempDeviceNo = generateDeviceNo();
    console.warn("[Device] Using temporary regenerated device number:", tempDeviceNo);
    return tempDeviceNo;
  }
}

/**
 * 清除设备号
 *
 * 从本地存储中删除设备号，下次调用getDeviceNo时会重新生成
 * 适用于重置设备标识的场景
 *
 * @throws {Error} 当清除操作失败时会记录错误但不抛出异常
 */
export function clearDeviceNo(): void {
  try {
    localStorage.removeItem(DEVICE_NO_KEY);
    console.log("[Device] Device number cleared successfully");
  } catch (error) {
    console.error("[Device] Error clearing device number:", error);
  }
}

/**
 * 检查是否已有设备号
 *
 * 检查本地存储中是否存在设备号，不会触发设备号的生成
 *
 * @returns 如果存在设备号返回true，否则返回false
 * @throws {Error} 当检查操作失败时返回false而不抛出异常
 */
export function hasDeviceNo(): boolean {
  try {
    const deviceNo = localStorage.getItem(DEVICE_NO_KEY);
    const exists = !!deviceNo;
    console.log(`[Device] Device number exists: ${exists}`);
    return exists;
  } catch (error) {
    console.error("[Device] Error checking device number:", error);
    return false;
  }
}

// ==================== 工具函数 ====================

/**
 * 获取当前设备号的平台类型
 *
 * @returns 设备号对应的平台类型，如果没有设备号则返回"unknown"
 */
export function getDevicePlatform(): PlatformType {
  try {
    const deviceNo = localStorage.getItem(DEVICE_NO_KEY);
    if (!deviceNo) {
      return "unknown";
    }

    if (deviceNo.startsWith("android_")) {
      return "android";
    } else if (deviceNo.startsWith("ios_")) {
      return "ios";
    }

    return "unknown";
  } catch (error) {
    console.error("[Device] Error getting device platform:", error);
    return "unknown";
  }
}

/**
 * 验证设备号格式是否正确
 *
 * @param deviceNo 要验证的设备号
 * @returns 如果格式正确返回true，否则返回false
 */
export function validateDeviceNo(deviceNo: string): boolean {
  if (!deviceNo || typeof deviceNo !== 'string') {
    return false;
  }

  // Android格式验证: android_xxxxxxxxxxxxxxxx (16位十六进制)
  const androidPattern = /^android_[0-9a-f]{16}$/;
  if (androidPattern.test(deviceNo)) {
    return true;
  }

  // iOS格式验证: ios_XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX (UUID格式)
  const iosPattern = /^ios_[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$/;
  if (iosPattern.test(deviceNo)) {
    return true;
  }

  return false;
}
