/**
 * 设备管理工具
 */

import { safeLocalStorage } from "../utils";

const DEVICE_NO_KEY = "device_no";
const localStorage = safeLocalStorage();

/**
 * 生成随机的Android设备号
 * 格式: android_xxxxxxxxxxxxxxxx (16位随机字符)
 */
function generateAndroidDeviceNo(): string {
  const chars = "0123456789abcdef";
  let result = "android_";
  for (let i = 0; i < 16; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 生成随机的iOS设备号
 * 格式: ios_XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX (UUID格式)
 */
function generateIOSDeviceNo(): string {
  const chars = "0123456789ABCDEF";
  const sections = [8, 4, 4, 4, 12];
  let result = "ios_";
  
  for (let i = 0; i < sections.length; i++) {
    if (i > 0) result += "-";
    for (let j = 0; j < sections[i]; j++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
  }
  
  return result;
}

/**
 * 检测当前平台类型
 */
function detectPlatform(): "android" | "ios" | "unknown" {
  if (typeof navigator === "undefined") return "unknown";
  
  const userAgent = navigator.userAgent.toLowerCase();
  
  if (/android/.test(userAgent)) {
    return "android";
  } else if (/iphone|ipad|ipod/.test(userAgent)) {
    return "ios";
  }
  
  return "unknown";
}

/**
 * 生成设备号
 * 根据当前平台自动选择生成Android或iOS设备号
 * 如果无法检测平台，默认生成Android设备号
 */
function generateDeviceNo(): string {
  const platform = detectPlatform();
  
  switch (platform) {
    case "ios":
      return generateIOSDeviceNo();
    case "android":
    case "unknown":
    default:
      return generateAndroidDeviceNo();
  }
}

/**
 * 获取设备号
 * 如果本地存储中没有设备号，则生成一个新的并存储
 * @returns 设备号
 */
export function getDeviceNo(): string {
  try {
    // 先尝试从本地存储获取
    let deviceNo = localStorage.getItem(DEVICE_NO_KEY);
    
    if (!deviceNo) {
      // 如果没有，则生成新的设备号
      deviceNo = generateDeviceNo();
      // 存储到本地
      localStorage.setItem(DEVICE_NO_KEY, deviceNo);
      console.log("Generated new device number:", deviceNo);
    } else {
      console.log("Retrieved existing device number:", deviceNo);
    }
    
    return deviceNo;
  } catch (error) {
    console.error("Error getting device number:", error);
    // 如果出错，返回一个临时的设备号
    return generateDeviceNo();
  }
}

/**
 * 重新生成设备号
 * 生成新的设备号并覆盖本地存储
 * @returns 新的设备号
 */
export function regenerateDeviceNo(): string {
  try {
    const newDeviceNo = generateDeviceNo();
    localStorage.setItem(DEVICE_NO_KEY, newDeviceNo);
    console.log("Regenerated device number:", newDeviceNo);
    return newDeviceNo;
  } catch (error) {
    console.error("Error regenerating device number:", error);
    return generateDeviceNo();
  }
}

/**
 * 清除设备号
 * 从本地存储中删除设备号
 */
export function clearDeviceNo(): void {
  try {
    localStorage.removeItem(DEVICE_NO_KEY);
    console.log("Device number cleared");
  } catch (error) {
    console.error("Error clearing device number:", error);
  }
}

/**
 * 检查是否已有设备号
 * @returns 是否存在设备号
 */
export function hasDeviceNo(): boolean {
  try {
    const deviceNo = localStorage.getItem(DEVICE_NO_KEY);
    return !!deviceNo;
  } catch (error) {
    console.error("Error checking device number:", error);
    return false;
  }
}
